<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
    <!-- 关闭 Logback 状态日志 -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

    <!-- 定义颜色变量 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />

    <!-- 使用环境变量 -->
    <property name="LOG_FILE_PATH" value="${LOG_FILE_PATH:-logs}"/>
    <property name="LOG_FILE_ENABLED" value="${LOG_FILE_ENABLED:-false}"/>
    <property name="LOG_ERROR_FILE_ENABLED" value="${LOG_ERROR_FILE_ENABLED:-false}"/>
    <property name="LOG_SQL_ENABLED" value="${LOG_SQL_ENABLED:-false}"/>
    <property name="MAX_FILE_SIZE" value="${LOG_MAX_FILE_SIZE:-100MB}"/>
    <property name="MAX_HISTORY" value="${LOG_MAX_HISTORY:-30}"/>
    <property name="TOTAL_SIZE_CAP" value="${LOG_TOTAL_SIZE_CAP:-3GB}"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%clr(%d{HH:mm:ss.SSS}){faint} %clr(%-5level){bold} %clr(%logger{36}){cyan} %clr(%msg){green}%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 应用程序日志 -->
    <logger name="com.qing.expert" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- SQL日志 - 关闭以简化输出 -->
    <logger name="com.qing.expert.mapper" level="OFF" additivity="false"/>

    <!-- 严格控制框架日志 - 只显示关键错误 -->
    <logger name="org.springframework" level="OFF"/>
    <logger name="org.springframework.security" level="OFF"/>
    <logger name="org.springframework.web" level="OFF"/>
    <logger name="org.springframework.boot" level="ERROR"/>
    <logger name="org.springframework.context" level="OFF"/>
    <logger name="org.springframework.beans" level="OFF"/>
    <logger name="org.springframework.core" level="OFF"/>
    <logger name="org.springframework.data" level="OFF"/>
    <logger name="org.springframework.aop" level="OFF"/>
    <logger name="org.springframework.transaction" level="OFF"/>
    <logger name="org.springframework.orm" level="OFF"/>
    <logger name="org.springframework.jdbc" level="OFF"/>
    <logger name="org.apache.catalina" level="OFF"/>
    <logger name="org.apache.tomcat" level="OFF"/>
    <logger name="org.apache.coyote" level="OFF"/>
    <logger name="org.hibernate" level="OFF"/>
    <logger name="com.baomidou.mybatisplus" level="OFF"/>
    <logger name="org.mybatis" level="OFF"/>
    <logger name="com.zaxxer.hikari" level="OFF"/>
    <logger name="org.springframework.boot.autoconfigure" level="OFF"/>
    <logger name="org.springframework.boot.web.embedded" level="OFF"/>
    <logger name="org.springframework.boot.actuate" level="OFF"/>
    <logger name="org.springframework.security.web" level="OFF"/>
    <logger name="org.springframework.web.servlet" level="OFF"/>
    <logger name="com.mysql.cj" level="OFF"/>
    <logger name="io.lettuce" level="OFF"/>
    <logger name="io.netty" level="OFF"/>
    <logger name="org.apache.http" level="OFF"/>
    <logger name="org.springframework.cache" level="OFF"/>
    <logger name="ch.qos.logback" level="OFF"/>
    <logger name="org.slf4j" level="OFF"/>
    <logger name="com.fasterxml.jackson" level="OFF"/>
    <logger name="org.yaml.snakeyaml" level="OFF"/>
    <logger name="org.reflections" level="OFF"/>
    <logger name="springfox" level="OFF"/>
    <logger name="io.swagger" level="OFF"/>
    <logger name="org.springdoc" level="OFF"/>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>

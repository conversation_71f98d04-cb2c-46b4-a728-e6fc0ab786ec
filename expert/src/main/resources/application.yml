server:
  port: ${SERVER_PORT:9090}
  servlet:
    context-path: ${SERVER_CONTEXT_PATH:/api}

spring:
  application:
    name: ${APP_NAME:expert}

  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:expert_db}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:1234}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

  # JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# 微信支付配置开关（默认禁用，需要时可在生产环境启用）
wechat:
  pay:
    config:
      enabled: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: "null"
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # SQL日志已关闭，如需调试可取消注释
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations:
    - classpath:mapper/**/*Mapper.xml
  type-aliases-package: com.qing.expert.entity

# Swagger配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.qing.expert.controller



# 自定义配置
expert:
  # 日志配置
  log:
    file-enabled: ${LOG_FILE_ENABLED:false}
    file-path: ${LOG_FILE_PATH:logs/}
    sql-file-enabled: ${LOG_SQL_ENABLED:false}
    error-file-enabled: ${LOG_ERROR_FILE_ENABLED:false}
    max-file-size: ${LOG_MAX_FILE_SIZE:100MB}
    max-history: ${LOG_MAX_HISTORY:30}
    total-size-cap: ${LOG_TOTAL_SIZE_CAP:3GB}

  # JWT配置
  jwt:
    secret: ${JWT_SECRET:mySecretKeyForDevelopmentOnlyPleaseChangeInProduction1234567890}
    expiration: ${JWT_EXPIRATION:86400000}
    header: ${JWT_HEADER:Authorization}
    prefix: ${JWT_PREFIX:Bearer }

  # 文件存储配置
  file:
    upload-path: ${UPLOAD_PATH:uploads/}
    max-size: ${UPLOAD_MAX_SIZE:10485760}
    allowed-types: ${UPLOAD_ALLOWED_TYPES:jpg,jpeg,png,gif,pdf,doc,docx}

  # 微信小程序配置 (已迁移到数据库管理)
  # 微信配置不再从环境变量读取，而是从数据库system_configs表中动态加载
  # 请通过管理后台 -> 系统配置 -> 微信配置 进行设置

  # 微信支付配置
  wechat-pay:
    mchid: ${WECHAT_PAY_MCHID:}
    private-key-path: ${WECHAT_PAY_PRIVATE_KEY_PATH:}
    merchant-serial-number: ${WECHAT_PAY_MERCHANT_SERIAL_NUMBER:}
    api-v3-key: ${WECHAT_PAY_API_V3_KEY:}

  # 阿里云OSS配置
  oss:
    endpoint: ${OSS_ENDPOINT:}
    access-key-id: ${OSS_ACCESS_KEY_ID:}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET:}
    bucket-name: ${OSS_BUCKET_NAME:}
    domain: ${OSS_DOMAIN:}

  # 邮件配置
  mail:
    host: ${MAIL_HOST:}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    from: ${MAIL_FROM:}

  # 短信配置
  sms:
    access-key-id: ${SMS_ACCESS_KEY_ID:}
    access-key-secret: ${SMS_ACCESS_KEY_SECRET:}
    sign-name: ${SMS_SIGN_NAME:}
    template-code: ${SMS_TEMPLATE_CODE:}

# 日志配置
logging:
  level:
    root: ${LOG_LEVEL:info}
    com.qing.expert: info
    com.baomidou.mybatisplus: warn
    org.mybatis: warn
  pattern:
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr(%-5level){bold} %clr(%logger{36}){cyan} %clr(%msg){green}%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
